from typing import List, Optional, Dict, Any

# Simple data classes for internal use
class Word:
    def __init__(self, start: float, end: float, word: str, probability: float):
        self.start = start
        self.end = end
        self.word = word
        self.probability = probability

class Segment:
    def __init__(self, start: float, end: float, text: str, speaker: str,
                 words: List[Word] = None, duration: Optional[float] = None,
                 avg_logprob: Optional[float] = None):
        self.start = start
        self.end = end
        self.text = text
        self.speaker = speaker
        self.words = words or []
        self.duration = duration
        self.avg_logprob = avg_logprob

# For Cog output, we'll use simple dictionaries and lists
class Output:
    def __init__(self, segments: List[Dict[str, Any]], language: Optional[str] = None,
                 num_speakers: Optional[int] = None, debug_info: Optional[List[str]] = None,
                 speaker_embeddings: Optional[Dict] = None):
        self.segments = segments
        self.language = language
        self.num_speakers = num_speakers
        self.debug_info = debug_info or []
        self.speaker_embeddings = speaker_embeddings
