# predict.py
import base64
import datetime
import os
import time
from typing import Dict, List, Optional
from cog import BasePredictor, BaseModel, Input
from lib.models import Output, Segment


# Import modules
from lib.audio_utils import download_file, get_wave_file
from lib.transcribe import Transcribe
from lib.merge_cosine import Merge
from lib.speaker_profile_handler import SpeakerProfileHandler


class SpeakerProfile(BaseModel):
    name: str
    embedding: List[float]
    confidence: Optional[float] = None
    metadata: Optional[Dict] = None

class KnownSpeakers(BaseModel):
    speakers: Dict[str, SpeakerProfile]

class Predictor(BasePredictor):

    def setup(self):
        self.transcribe = Transcribe()
        self.merge = Merge()
        self.speaker_handler = SpeakerProfileHandler()

    def predict(
        self,
        file_url: Optional[str] = Input(
            description="Or provide a direct audio file URL."
        ),
        num_speakers: Optional[int] = Input(
            description="Number of speakers, leave empty to autodetect.",
            ge=1,
            le=50
        ),
        translate: bool = Input(
            description="Translate the speech into English.", default=False
        ),
        language: Optional[str] = Input(
            description="Language of the spoken words as a language code like 'en'. Leave empty to auto-detect language."
        ),
        prompt: Optional[str] = Input(
            description="Vocabulary: provide names, acronyms, and loanwords in a list. Use punctuation for best accuracy."
        ),
        vad_filter: bool = Input(
            description="Enable VAD filter.", default=True
        ),
        diarize_v2: bool = Input(
            description="Diarization Model V2.", default=False
        ),
        diarize_only: bool = Input(
            description="Diarization Only", default=False
        ),
        debug_info: bool = Input(
            description="Enable Debug Info.", default=False
        ),
        known_speakers: Optional[str] = Input(
            description="""JSON string containing known speaker profiles. Format:
            {
                "speakers": {
                    "speaker_id1": {
                        "name": "John Doe",
                        "embedding": [0.1, 0.2, ...],
                        "confidence": 0.95,
                        "metadata": {"age": 30, "gender": "male"}
                    },
                    "speaker_id2": {
                        "name": "Jane Smith",
                        "embedding": [0.3, 0.4, ...],
                        "confidence": 0.92
                    }
                }
            }"""
        ),
        download_only: bool = Input(
            description="Download file only.", default=False
        ),
    ) -> Output:
        try:
            print("starting inference")
            if download_only:
                wave_filename = download_file(file_url)
            else:
                wave_filename = get_wave_file(None, file_url, None, True)
            print("starting inference - wave: " + wave_filename )
            if not diarize_only:
                segments, transcript_info = self.transcribe.speech_to_text(
                    wave_filename,
                    num_speakers,
                    prompt,
                    language,
                    translate,
                    vad_filter
                )
            else:
                segments = []
                transcript_info = {"language": language}

            time_merging_start = time.time()
            print("Starting diarization")
            time_diarization_start = time.time()

            # Initialize appropriate diarization processor
            if diarize_v2:
                from lib.diarization_v2 import DiarizationProcessor
            else:
                from lib.diarization_v1 import DiarizationProcessor

            diarization_processor = DiarizationProcessor()

            # Process diarization
            parsed_speakers = self.speaker_handler.parse_known_speakers(known_speakers)
            
            if diarize_v2:
                diarize_segments, detected_num_speakers, speaker_labels, speaker_emb_map = \
                    diarization_processor.process(
                        wave_filename, 
                        num_speakers,
                        existing_embeddings=parsed_speakers
                    )
            else:
                diarize_segments, detected_num_speakers, speaker_labels, speaker_emb_map = \
                    diarization_processor.process(
                        wave_filename, 
                        num_speakers,
                        existing_embeddings=parsed_speakers
                    )

            time_diarization_end = time.time()
            print(f"Finished diarization in {time_diarization_end - time_diarization_start:.5f} seconds")

            # Merge segments
            merged_segments = self.merge.process(segments, diarize_segments, speaker_emb_map)
            time_merging_end = time.time()
            print(f"Finished merging in {time_merging_end - time_merging_start:.5f} seconds")

            print("Done with inference")
            speaker_results = self.speaker_handler.format_speaker_results(speaker_emb_map)

            segments = [Segment(**s) for s in merged_segments]
            return Output(
                segments=merged_segments,
                language=transcript_info.language,
                num_speakers=detected_num_speakers
            )

        except Exception as e:
            raise RuntimeError("Error running inference with local model", e)

        finally:
            if os.path.exists(wave_filename):
                os.remove(wave_filename)

    def convert_time(self, secs, offset_seconds=0):
        return datetime.timedelta(seconds=(round(secs) + offset_seconds))
